# llm_evaluation.py
"""A compact toolbox for **benchmarking structured JSON outputs** from multiple LLMs.

It blends
* **deterministic** checks (exact‑match, schema fidelity),
* **fuzzy/semantic** comparisons (string similarity, embeddings, LLM‑judge), and
* several **easy‑to‑read composite scores**
so stakeholders can quickly grasp overall quality.

> **New in v0.4** – three *unique* yet intuitive metrics:
> 1. **Key‑set Jaccard** – how similar are the two JSON structures?
> 2. **Average String Similarity** – mean fuzzy score across all textual fields.
> 3. **Numeric MAPE Accuracy** – 1‑MAPE over all numeric fields for scale‑aware precision.
"""
from __future__ import annotations

import json
import os
import math
import difflib
from typing import Any, Dict, Iterable, List, Tuple
from dataclasses import dataclass

import numpy as np
import pandas as pd
from tqdm import tqdm

# Optional third‑party lib for faster string distance
try:
    import Levenshtein  # type: ignore
except ImportError:  # pragma: no cover
    Levenshtein = None

################################################################################
# ------------------------------  BASIC HELPERS  ----------------------------- #
################################################################################

def _flatten(d: Dict[str, Any], parent_key: str = "", sep: str = ".") -> Dict[str, Any]:
    """Flatten nested dict into a single‑level dict using dotted keys."""
    items: List[Tuple[str, Any]] = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(_flatten(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)

################################################################################
# -------------------------  LIGHTWEIGHT ARITHMETIC  ------------------------- #
################################################################################

def count_null_fields(record: Dict[str, Any]) -> int:
    """Number of *null / empty* values in **one** JSON record."""
    return sum(1 for v in _flatten(record).values() if v in (None, "", [], {}))


def count_field_matches(ref: Dict[str, Any], cand: Dict[str, Any]) -> int:
    """Exact (===) equality count between reference & candidate JSON."""
    ref_flat, cand_flat = _flatten(ref), _flatten(cand)
    return sum(1 for k, v in ref_flat.items() if cand_flat.get(k) == v)


def min_max_matches(match_counts: List[int]) -> Tuple[int, int, float]:
    """Return *(min, max, mean)* of match counts list."""
    if not match_counts:
        return (0, 0, 0.0)
    return min(match_counts), max(match_counts), float(np.mean(match_counts))

################################################################################
# ---------------------------  STRUCTURAL METRICS  --------------------------- #
################################################################################

def schema_compliance(ref: Dict[str, Any], cand: Dict[str, Any]) -> Dict[str, int]:
    """Compare candidate against *implicit* schema from reference JSON.

    Returns counts of **missing**, **extra**, and **type_mismatch** keys.
    """
    ref_flat, cand_flat = _flatten(ref), _flatten(cand)
    missing = [k for k in ref_flat if k not in cand_flat]
    extra = [k for k in cand_flat if k not in ref_flat]
    type_mismatch = [
        k for k in ref_flat if k in cand_flat and not isinstance(cand_flat[k], type(ref_flat[k]))
    ]
    return {
        "missing": len(missing),
        "extra": len(extra),
        "type_mismatch": len(type_mismatch),
    }

################################################################################
# ---------------------------  FUZZY COMPARISONS  ---------------------------- #
################################################################################

def string_similarity(a: Any, b: Any) -> float:
    """Levenshtein (or difflib) ratio normalised 0‑1."""
    if a is None or b is None:
        return 0.0
    a, b = str(a), str(b)
    if Levenshtein:
        return Levenshtein.ratio(a, b)
    return difflib.SequenceMatcher(None, a, b).ratio()


def numeric_tolerance_accuracy(a: Any, b: Any, rel_tol: float = 1e-6) -> float:
    """Accuracy = 1 − relative_error (clipped 0‑1)."""
    try:
        a_val, b_val = float(a), float(b)
    except (TypeError, ValueError):
        return 0.0
    if a_val == b_val:
        return 1.0
    error = abs(a_val - b_val) / (abs(a_val) + 1e-12)
    return max(0.0, 1.0 - error / rel_tol)

################################################################################
# ---------------  ✨ NEW: UNIQUE, EASY‑TO‑GRASP METRICS  -------------------- #
################################################################################

def key_jaccard(ref: Dict[str, Any], cand: Dict[str, Any]) -> float:
    """Jaccard similarity of the *key sets* (structure overlap)."""
    ref_keys = set(_flatten(ref).keys())
    cand_keys = set(_flatten(cand).keys())
    if not ref_keys and not cand_keys:
        return 1.0
    return len(ref_keys & cand_keys) / len(ref_keys | cand_keys)


def average_string_similarity(ref: Dict[str, Any], cand: Dict[str, Any]) -> float:
    """Mean fuzzy similarity across **all textual fields** (0‑1)."""
    ref_flat, cand_flat = _flatten(ref), _flatten(cand)
    sims: List[float] = []
    for k, v in ref_flat.items():
        if isinstance(v, str) and isinstance(cand_flat.get(k), str):
            sims.append(string_similarity(v, cand_flat[k]))
    if not sims:
        return 1.0  # nothing to compare
    return float(np.mean(sims))


def numeric_mape_accuracy(ref: Dict[str, Any], cand: Dict[str, Any]) -> float:
    """1 ‑ Mean Absolute Percentage Error across **numeric** fields (0‑1)."""
    ref_flat, cand_flat = _flatten(ref), _flatten(cand)
    errors: List[float] = []
    for k, v in ref_flat.items():
        if isinstance(v, (int, float)) and isinstance(cand_flat.get(k), (int, float)):
            denom = abs(v) + 1e-12
            errors.append(abs(v - cand_flat[k]) / denom)
    if not errors:
        return 1.0
    mape = float(np.mean(errors))
    return max(0.0, 1.0 - mape)  # convert error ➜ accuracy

################################################################################
# -----------------------  SEMANTIC / LLM‑BASED METRICS  --------------------- #
################################################################################

def embedding_similarity(ref_text: str, cand_text: str, model: str = "text-embedding-3-small") -> float:
    """Cosine similarity of OpenAI embeddings (0‑1). Fallback ➜ string_similarity."""
    try:
        import openai  # local import

        openai.api_key = os.getenv("OPENAI_API_KEY")
        if not openai.api_key:
            raise EnvironmentError("OPENAI_API_KEY not set")

        ref_emb = openai.Embedding.create(model=model, input=ref_text)["data"][0]["embedding"]
        cand_emb = openai.Embedding.create(model=model, input=cand_text)["data"][0]["embedding"]
        ref_arr, cand_arr = np.array(ref_emb), np.array(cand_emb)
        sim = float(ref_arr @ cand_arr / (np.linalg.norm(ref_arr) * np.linalg.norm(cand_arr) + 1e-12))
        return sim
    except Exception:
        return string_similarity(ref_text, cand_text)


def hallucination_flag(prompt: str, ref_ans: str, cand_ans: str, judge_model: str = "gemini-pro") -> int:
    """Returns **1** if judge LLM says candidate hallucinated vs prompt/ref."""
    try:
        from llm_utils import call_judge  # your thin wrapper

        q = (
            "You are a strict fact‑checker. Given <prompt>, <reference answer>, and <candidate>, "
            "answer ONLY 'yes' or 'no': Does the candidate include facts not present in the prompt or reference?"
        )
        resp = call_judge(
            judge_model,
            q.replace("<prompt>", prompt)
            .replace("<reference answer>", ref_ans)
            .replace("<candidate>", cand_ans),
            temperature=0.0,
        )
        return 1 if resp.strip().lower().startswith("yes") else 0
    except Exception:
        return 0  # assume safe

################################################################################
# -----------------------  EASY‑TO‑UNDERSTAND SCORES  ----------------------- #
################################################################################

def coverage_ratio(ref: Dict[str, Any], cand: Dict[str, Any]) -> float:
    """Fraction of expected fields that are *present & non‑null* in candidate."""
    ref_flat = _flatten(ref)
    total = len(ref_flat)
    if total == 0:
        return 1.0
    cand_flat = _flatten(cand)
    missing_count = len([k for k in ref_flat if k not in cand_flat])
    null_count = count_null_fields(cand)
    covered = total - missing_count - null_count
    return max(0.0, covered / total)


def exact_match_ratio(ref: Dict[str, Any], cand: Dict[str, Any]) -> float:
    """Exact‑match count / total fields (0‑1)."""
    ref_flat = _flatten(ref)
    total = len(ref_flat)
    if total == 0:
        return 1.0
    matches = count_field_matches(ref, cand)
    return matches / total


def overall_score(coverage: float, exact: float, hallucinated: int | None = None) -> float:
    """Simple weighted blend ➜ overall quality score (0‑1)."""
    base = 0.4 * coverage + 0.4 * exact
    base += 0.2 * min(coverage, exact)  # bonus for consistency
    if hallucinated is not None:
        base *= 1.0 if hallucinated == 0 else 0.8  # penalise hallucinations 20%
    return round(base, 4)

################################################################################
# ------------------------------  ORCHESTRATION  ----------------------------- #
################################################################################

def evaluate_record(ref: Dict[str, Any], cand: Dict[str, Any], prompt: str | None = None) -> Dict[str, Any]:
    """Compute **all** metrics for one reference/candidate pair."""
    out: Dict[str, Any] = {}

    # — Arithmetic & schema
    out["null_count"] = count_null_fields(cand)
    out["exact_match"] = exact_match = count_field_matches(ref, cand)
    schema_stats = schema_compliance(ref, cand)
    out.update({f"schema_{k}": v for k, v in schema_stats.items()})

    # — Coverage & ratios
    out["coverage_ratio"] = cov = coverage_ratio(ref, cand)
    out["exact_match_ratio"] = ex_ratio = exact_match_ratio(ref, cand)

    # — Unique metrics
    out["key_jaccard"] = key_jaccard(ref, cand)
    out["avg_string_sim"] = average_string_similarity(ref, cand)
    out["numeric_mape_accuracy"] = numeric_mape_accuracy(ref, cand)

    # — Optional hallucination
    hall_flag: int | None = None
    if prompt is not None:
        hall
