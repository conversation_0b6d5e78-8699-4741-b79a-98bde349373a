import json
from i import evaluate_record

with open("a.json") as f:
    ref = json.load(f)          # ← should be a dict

with open("g.json") as f:
    cand_raw = json.load(f)     # could still be str if nested

# If either object is still a string, parse it once more
if isinstance(ref, str):
    ref = json.loads(ref)
if isinstance(cand_raw, str):
    cand = json.loads(cand_raw)
else:
    cand = cand_raw

metrics = evaluate_record(ref, cand)
print(metrics)
